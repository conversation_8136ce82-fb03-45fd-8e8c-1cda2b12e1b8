import { supabase } from "@/src/config/supabase";
import { TABLES, COLUMNS, BUCKETS } from "../constants";
import { Database, Tables } from "@/src/types/supabase";
import { getProductBaseImagePath, getProductVariantImagePath } from "@/src/utils/storage-paths";

export type BusinessSortBy =
  | "name_asc"
  | "name_desc"
  | "created_asc"
  | "created_desc"
  | "likes_asc"
  | "likes_desc"
  | "subscriptions_asc"
  | "subscriptions_desc"
  | "rating_asc"
  | "rating_desc"
  | "newest";

export type ProductSortBy =
  | "name_asc"
  | "name_desc"
  | "created_asc"
  | "created_desc"
  | "price_asc"
  | "price_desc"
  | "newest";

// Upload size limits (75MB for up to 5 images)
const MAX_UPLOAD_SIZE_BYTES = 75 * 1024 * 1024; // 75MB
const MAX_IMAGES_PER_GROUP = 5;

/**
 * Fetches a product/service by its ID and business ID.
 * @param itemId The ID of the product/service.
 * @param businessId The ID of the business.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
/**
 * Fetches a product/service by its ID and business ID from the 'products_services' table.
 * This function retrieves specific product/service details including image URLs and name.
 * @param itemId The unique identifier of the product or service.
 * @param businessId The unique identifier of the business that owns the product/service.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: The product/service object, or null if not found.
 *   - `error`: An Error object if the query fails, otherwise null.
 */
export const fetchProductServiceById = async (itemId: string, businessId: string) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(`${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.NAME}`)
    .eq(COLUMNS.ID, itemId)
    .eq(COLUMNS.BUSINESS_ID, businessId)
    .single();
};

/**
 * Updates an existing product/service in the database.
 * @param itemId The ID of the product/service to update.
 * @param businessId The ID of the business owning the product/service.
 * @param dataToUpdate The data to update.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
/**
 * Updates an existing product/service in the 'products_services' table.
 * This function updates the specified fields for a product/service identified by its ID and business ID.
 * @param itemId The unique identifier of the product/service to update.
 * @param businessId The unique identifier of the business owning the product/service.
 * @param dataToUpdate The partial data object containing fields to be updated.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: The updated product/service object, or null if the update fails.
 *   - `error`: An Error object if the update fails, otherwise null.
 */
export const updateProductServiceData = async (
  itemId: string,
  businessId: string,
  dataToUpdate: Partial<Tables<'products_services'>>
) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .update(dataToUpdate)
    .eq(COLUMNS.ID, itemId)
    .eq(COLUMNS.BUSINESS_ID, businessId)
    .select(
      `${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`
    )
    .single();
};

/**
 * Inserts a new product/service into the database.
 * @param dataToInsert The data to insert.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
/**
 * Inserts a new product/service into the 'products_services' table.
 * This function adds a new product or service entry to the database.
 * @param dataToInsert The data object containing the details of the product/service to be inserted.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: The newly inserted product/service object, or null if the insertion fails.
 *   - `error`: An Error object if the insertion fails, otherwise null.
 */
export const insertProductServiceData = async (dataToInsert: Partial<Tables<'products_services'>>) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .insert(dataToInsert)
    .select(
      `${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`
    )
    .single();
};

/**
 * Deletes a product/service by its ID and business ID.
 * @param itemId The ID of the product/service to delete.
 * @param businessId The ID of the business owning the product/service.
 * @returns Promise<{ error: Error | null }>
 */
/**
 * Deletes a product/service by its ID and business ID from the 'products_services' table.
 * This function removes a specific product or service entry from the database.
 * @param itemId The unique identifier of the product/service to delete.
 * @param businessId The unique identifier of the business that owns the product/service.
 * @returns A Promise that resolves to an object containing:
 *   - `error`: An Error object if the deletion fails, otherwise null.
 */
export const deleteProductServiceById = async (itemId: string, businessId: string) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .delete()
    .eq(COLUMNS.ID, itemId)
    .eq(COLUMNS.BUSINESS_ID, businessId);
};

/**
 * Helper function to handle multiple image uploads to Supabase storage.
 * Each image is processed individually and compressed to <100KB.
 * Total input: up to 5 × 15MB = 75MB, Total output: ~5 × 100KB = 500KB.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @param pathType The type of path (base or variant).
 * @param variantId The ID of the variant (if pathType is variant).
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
/**
 * Handles the upload of multiple images to Supabase storage, including compression, removal of old images, and generation of public URLs.
 * Each image is processed individually and compressed to <100KB.
 * Total input: up to 5 × 15MB = 75MB, Total output: ~5 × 100KB = 500KB.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload (can contain null for skipped files).
 * @param existingImageUrls An array of existing image URLs associated with the product/variant.
 * @param removedIndices An array of indices corresponding to `existingImageUrls` that should be removed from storage.
 * @param pathType The type of path for image storage ('base' for product, 'variant' for product variant).
 * @param variantId The ID of the variant (required if `pathType` is 'variant').
 * @returns A Promise that resolves to an object containing:
 *   - `urls`: An array of public URLs for all images (existing and newly uploaded) after processing.
 *   - `error`: A string containing an error message if any operation fails, otherwise undefined.
 */
export async function handleMultipleImageUpload(
  userId: string,
  productId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = [],
  pathType: 'base' | 'variant' = 'base',
  variantId?: string
): Promise<{ urls: string[]; error?: string }> {
  // Validate input parameters
  if (!userId || typeof userId !== 'string') {
    console.error('Invalid userId provided to handleMultipleImageUpload:', userId);
    return { urls: [], error: `Invalid userId: expected string, got ${typeof userId}` };
  }

  if (!productId || typeof productId !== 'string') {
    console.error('Invalid productId provided to handleMultipleImageUpload:', productId);
    return { urls: [], error: `Invalid productId: expected string, got ${typeof productId}` };
  }

  // Validate variantId if pathType is 'variant'
  if (pathType === 'variant' && (!variantId || typeof variantId !== 'string')) {
    console.error('Invalid variantId provided for variant path type:', variantId);
    return { urls: [], error: `Invalid variantId: expected string when pathType is 'variant', got ${typeof variantId}` };
  }

  // Validate upload limits
  const validImageFiles = imageFiles.filter(file => file !== null) as File[];

  console.log(`=== Image Upload Handler Debug ===`);
  console.log(`Path type: ${pathType}, Variant ID: ${variantId || 'N/A'}`);
  console.log(`Valid image files: ${validImageFiles.length}`);
  console.log(`Existing images: ${existingImageUrls.length}`);
  console.log(`Removed indices: ${removedIndices.length}`);

  if (validImageFiles.length === 0 && removedIndices.length === 0) {
    console.log("No image operations to perform, returning existing URLs");
    return { urls: existingImageUrls };
  }

  if (validImageFiles.length > MAX_IMAGES_PER_GROUP) {
    return { urls: [], error: `Maximum of ${MAX_IMAGES_PER_GROUP} images allowed per ${pathType === 'variant' ? 'variant' : 'product'}.` };
  }

  const totalUploadSize = validImageFiles.reduce((total, file) => total + file.size, 0);
  if (totalUploadSize > MAX_UPLOAD_SIZE_BYTES) {
    const totalSizeMB = (totalUploadSize / (1024 * 1024)).toFixed(1);
    const maxSizeMB = (MAX_UPLOAD_SIZE_BYTES / (1024 * 1024)).toFixed(0);
    return { urls: [], error: `Total upload size (${totalSizeMB}MB) exceeds the ${maxSizeMB}MB limit for ${pathType === 'variant' ? 'variant' : 'product'} images.` };
  }

  const bucketName = BUCKETS.BUSINESS;
  const urls: string[] = [...existingImageUrls];

  // Individual image paths are generated using scalable structure in getProductImagePath utility

  // Use admin client for storage operations to bypass RLS
  const supabaseClient = supabase;

  // First, handle removals
  for (const index of removedIndices) {
    if (index >= 0 && index < existingImageUrls.length) {
      const imageUrl = existingImageUrls[index];
      if (imageUrl) {
        try {
          console.log(`Removing image at index ${index}: ${imageUrl}`);

          // Extract the storage path from the URL
          // Parse the URL to extract the correct path
          const url = new URL(imageUrl);
          const pathParts = url.pathname.split('/');

          // The path will be in format like /storage/v1/object/public/business/userId/products/productId_name/image_0.webp
          // We need to extract the part after 'business/'
          const businessIndex = pathParts.findIndex(part => part === BUCKETS.BUSINESS);

          if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
            // Extract the path after 'business/'
            const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

            console.log(`Attempting to delete from storage path: ${storagePath}`);

            // Delete the file from storage using admin client
            const { error: deleteError } = await supabaseClient.storage
              .from(bucketName)
              .remove([storagePath]);

            if (deleteError && deleteError.message !== "The resource was not found") {
              console.error(`Error deleting image at index ${index}:`, deleteError);
              console.error(`Delete error details:`, deleteError);
              // Don't fail the entire operation for storage deletion errors
            } else {
              console.log(`Successfully deleted image at path: ${storagePath}`);
            }
          } else {
            console.warn(`Could not extract storage path from URL: ${imageUrl}`);
            console.warn(`URL pathname: ${url.pathname}`);
            console.warn(`Path parts:`, pathParts);
          }
        } catch (error) {
          console.error(`Error processing image URL for deletion at index ${index}:`, error);
          // Don't fail the entire operation for individual image deletion errors
        }

        // Remove from the URLs array regardless of storage deletion success
        // This ensures the database is updated even if storage deletion fails
        urls[index] = '';
      }
    }
  }

  // Filter out empty strings from the URLs array to actually remove the deleted images
  const filteredUrls = [...urls.filter(url => url !== '')];

  // Calculate the starting index for new images based on remaining images after deletions
  const startingImageIndex = filteredUrls.length;

  // Then, handle uploads
  for (let i = 0; i < imageFiles.length; i++) {
    const imageFile = imageFiles[i];
    if (!imageFile) {
      continue;
    }

    try {
      // Create path with scalable structure and precise timestamp to prevent caching
      const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);

      // Calculate the correct image index for naming (starting from the count of remaining images)
      const imageIndex = startingImageIndex + i;

      // Generate appropriate path based on type
      const imagePath = pathType === 'variant' && variantId
        ? getProductVariantImagePath(userId, productId, variantId, imageIndex, timestamp)
        : getProductBaseImagePath(userId, productId, imageIndex, timestamp);

      console.log(`Uploading image ${i} (index ${imageIndex}) to path: ${imagePath}`);

      // File is already compressed on client-side, just upload it
      const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

      const { error: uploadError } = await supabaseClient.storage
        .from(bucketName)
        .upload(imagePath, fileBuffer, {
          contentType: imageFile.type, // Use original file type (already compressed)
          upsert: true
        });

      if (uploadError) {
        console.error(`Failed to upload image ${i}:`, uploadError);
        continue;
      }

      const { data: urlData } = supabaseClient.storage
        .from(bucketName)
        .getPublicUrl(imagePath);

      if (!urlData?.publicUrl) {
        console.error(`Could not retrieve public URL for image ${i}`);
        continue;
      }

      console.log(`Successfully uploaded image ${i}, URL: ${urlData.publicUrl}`);

      // Append the new image URL to the filtered URLs array (after existing images)
      filteredUrls.push(urlData.publicUrl);
    } catch (error) {
      console.error(`Error processing image ${i}:`, error);
    }
  }

  console.log(`Image upload complete. Returning ${filteredUrls.length} URLs:`, filteredUrls);
  console.log(`=== End Image Upload Handler Debug ===`);

  // Return the final URLs array (existing images after deletions + new uploads)
  return { urls: filteredUrls };
}

/**
 * Helper function specifically for base product images
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
/**
 * Helper function specifically for base product images.
 * This function wraps `handleMultipleImageUpload` for base product image handling.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
export async function handleBaseProductImageUpload(
  userId: string,
  productId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = []
): Promise<{ urls: string[]; error?: string }> {
  return handleMultipleImageUpload(
    userId,
    productId,
    imageFiles,
    existingImageUrls,
    removedIndices,
    'base'
  );
}

/**
 * Helper function specifically for variant images.
 * This function wraps `handleMultipleImageUpload` for product variant image handling.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param variantId The ID of the variant.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
export async function handleVariantImageUpload(
  userId: string,
  productId: string,
  variantId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = []
): Promise<{ urls: string[]; error?: string }> {
  return handleMultipleImageUpload(
    userId,
    productId,
    imageFiles,
    existingImageUrls,
    removedIndices,
    'variant',
    variantId
  );
}

/**
 * Fetches a product/service by its ID.
 * @param productId The ID of the product/service.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
export const fetchProductById = async (productId: string): Promise<{ success: boolean; data: Tables<'products_services'> | null; error: string | null }> => {
  const { data, error } = await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      *,
      product_variants (
        ${COLUMNS.ID},
        ${COLUMNS.VARIANT_NAME},
        ${COLUMNS.VARIANT_VALUES},
        ${COLUMNS.BASE_PRICE},
        ${COLUMNS.DISCOUNTED_PRICE},
        ${COLUMNS.IS_AVAILABLE},
        ${COLUMNS.IMAGES},
        ${COLUMNS.FEATURED_IMAGE_INDEX}
      )
    `
    )
    .eq(COLUMNS.ID, productId)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .single();

  if (error) {
    console.error("Error fetching product by ID:", error);
    return { success: false, data: null, error: error.message };
  }
  return { success: true, data, error: null };
};

/**
 * Fetches more products from the same business.
 * @param businessId The ID of the business.
 * @param currentProductId The ID of the current product to exclude.
 * @param limit The maximum number of products to fetch.
 * @returns Promise<{ data: Tables<'products_services'>[] | null, error: Error | null }>
 */
export const fetchMoreProductsFromBusiness = async (
  businessId: string,
  currentProductId: string,
  limit: number = 8
) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.IMAGES},
      ${COLUMNS.FEATURED_IMAGE_INDEX},
      ${COLUMNS.BUSINESS_ID},
      ${COLUMNS.IS_AVAILABLE}
      `
    )
    .eq(COLUMNS.BUSINESS_ID, businessId)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .neq(COLUMNS.ID, currentProductId)
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .limit(limit);
};

/**
 * Fetches products from other businesses.
 * @param businessId The ID of the current business to exclude.
 * @param limit The maximum number of products to fetch.
 * @returns Promise<{ data: (Tables<'products_services'> & { business_slug: string })[] | null, error: Error | null }>
 */
export const fetchProductsFromOtherBusinesses = async (
  businessId: string,
  limit: number = 8
) => {
  // Get all online business IDs
  const { data: validBusinesses, error: businessError } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(COLUMNS.ID)
    .neq(COLUMNS.ID, businessId)
    .eq(COLUMNS.STATUS, 'online');

  if (businessError || !validBusinesses || validBusinesses.length === 0) {
    return { data: [], error: businessError };
  }

  const validBusinessIds = validBusinesses.map((b) => b.id);

  // Fetch products from valid businesses
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.IMAGES},
      ${COLUMNS.FEATURED_IMAGE_INDEX},
      ${COLUMNS.BUSINESS_ID},
      ${COLUMNS.IS_AVAILABLE},
      ${TABLES.BUSINESS_PROFILES}!${COLUMNS.BUSINESS_ID}(${COLUMNS.BUSINESS_SLUG})
      `
    )
    .in(COLUMNS.BUSINESS_ID, validBusinessIds)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .limit(limit);
};

/**
 * Searches for products belonging to a specific business by name.
 * @param userId The ID of the business owner.
 * @param query The search query string.
 * @returns Promise<{ data: Tables<'products_services'>[] | null, error: Error | null }>
 */
export const searchBusinessProducts = async (userId: string, query: string) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.SLUG},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BUSINESS_ID}
      `
    )
    .eq(COLUMNS.BUSINESS_ID, userId)
    .ilike(COLUMNS.NAME, `%${query}%`)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .order(COLUMNS.NAME)
    .limit(20);
};

/**
 * Fetches selected products by their IDs, ensuring they belong to a specific business.
 * @param userId The ID of the business owner.
 * @param productIds An array of product IDs to fetch.
 * @returns Promise<{ data: Tables<'products_services'>[] | null, error: Error | null }>
 */
export const getSelectedProducts = async (userId: string, productIds: string[]) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.SLUG},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BUSINESS_ID}
      `
    )
    .in(COLUMNS.ID, productIds)
    .eq(COLUMNS.BUSINESS_ID, userId)
    .eq(COLUMNS.IS_AVAILABLE, true);
};

/**
 * Fetches business products with pagination.
 * @param userId The ID of the business owner.
 * @param offset The offset for pagination.
 * @param limit The number of items to return.
 * @returns Promise<{ data: Tables<'products_services'>[] | null, error: Error | null, count: number | null }>
 */
export const getBusinessProducts = async (
  userId: string,
  offset: number,
  limit: number
) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.SLUG},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BUSINESS_ID}
      `,
      { count: "exact" }
    )
    .eq(COLUMNS.BUSINESS_ID, userId)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .range(offset, offset + limit - 1);
};

/**
 * Fetches a single product by its ID and the business ID.
 * @param userId The ID of the business owner.
 * @param productId The ID of the product.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
export const getProductByIdAndBusinessId = async (userId: string, productId: string) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.SLUG},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BUSINESS_ID}
      `
    )
    .eq(COLUMNS.ID, productId)
    .eq(COLUMNS.BUSINESS_ID, userId)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .single();
};

/**
 * Fetches products by their IDs.
 * @param productIds An array of product IDs.
 * @returns Promise<{ data: Tables<'products_services'>[] | null, error: Error | null }>
 */
export async function fetchProductsByIds(productIds: string[]) {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.SLUG},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BUSINESS_ID},
      ${COLUMNS.IS_AVAILABLE},
      ${COLUMNS.CREATED_AT},
      ${COLUMNS.UPDATED_AT}
    `
    )
    .in(COLUMNS.ID, productIds)
    .eq(COLUMNS.IS_AVAILABLE, true);
}

/**
 * Fetches IDs of online business profiles based on various filters.
 * @param city Optional city slug to filter by.
 * @param pincode Optional pincode to filter by.
 * @param locality Optional locality slug to filter by.
 * @param category Optional business category to filter by.
 * @returns Promise<{ data: Pick<Tables<'business_profiles'>, 'id'>[] | null, error: Error | null }>
 */
export const fetchOnlineBusinessIds = async (
  city: string | null,
  pincode: string | null,
  locality: string | null,
  category: string | null
) => {
  let query = supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(COLUMNS.ID)
    .eq(COLUMNS.STATUS, 'online');

  if (city) {
    query = query.eq(COLUMNS.CITY, city);
  }
  if (pincode) {
    query = query.eq(COLUMNS.PINCODE, pincode);
  }
  if (locality) {
    query = query.eq(COLUMNS.LOCALITY, locality);
  }
  if (category && category.trim()) {
    query = query.eq(COLUMNS.BUSINESS_CATEGORY, category.trim());
  }

  return await query;
};

/**
 * Counts products based on business IDs, product type, and product name.
 * @param businessIds An array of business IDs to filter by.
 * @param productType Optional product type to filter by.
 * @param productName Optional product name to filter by (case-insensitive).
 * @returns Promise<{ count: number | null, error: Error | null }>
 */
export const countProducts = async (
  businessIds: string[],
  productType: "physical" | "service" | null,
  productName: string | null
) => {
  let query = supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(COLUMNS.ID, { count: "exact" })
    .in(COLUMNS.BUSINESS_ID, businessIds)
    .eq(COLUMNS.IS_AVAILABLE, true);

  if (productType) {
    query = query.eq(COLUMNS.PRODUCT_TYPE, productType);
  }
  if (productName && productName.trim().length > 0) {
    query = query.ilike(COLUMNS.NAME, `%${productName.trim()}%`);
  }

  return await query;
};

/**
 * Fetches products with various filters, sorting, and pagination.
 * @param businessIds An array of business IDs to filter by.
 * @param productType Optional product type to filter by.
 * @param productName Optional product name to filter by (case-insensitive).
 * @param sortBy The sorting criteria.
 * @param offset The offset for pagination.
 * @param limit The number of items to return.
 * @returns Promise<{ data: Tables<'products_services'>[] | null, error: Error | null }>
 */
export const fetchProductsWithFilters = async (
  businessIds: string[],
  productType: "physical" | "service" | null,
  productName: string | null,
  sortBy: string,
  offset: number,
  limit: number
) => {
  let productsQuery = supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_ID}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.PRODUCT_TYPE},
        ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG},
        ${TABLES.BUSINESS_PROFILES}!${COLUMNS.BUSINESS_ID}(${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LATITUDE}, ${COLUMNS.LONGITUDE})
      `
    )
    .in(COLUMNS.BUSINESS_ID, businessIds)
    .eq(COLUMNS.IS_AVAILABLE, true);

  if (productType) {
    productsQuery = productsQuery.eq(COLUMNS.PRODUCT_TYPE, productType);
  }
  if (productName && productName.trim().length > 0) {
    productsQuery = productsQuery.ilike(COLUMNS.NAME, `%${productName.trim()}%`);
  }

  // Add sorting
  // NOTE: getSortingColumn and getSortingDirection are not defined in this file.
  // They are expected to be imported or defined elsewhere.
  // Assuming they are available in the context where this function is used.
  const sortColumn = getSortingColumn(sortBy, true); // true indicates product view
  const sortAscending = getSortingDirection(sortBy);

  // Special handling for price sorting to use discounted_price when available, otherwise base_price
  if (sortColumn === "price") {
    if (sortAscending) {
      productsQuery = productsQuery
        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: true, nullsFirst: false })
        .order(COLUMNS.BASE_PRICE, { ascending: true, nullsFirst: false });
    } else {
      productsQuery = productsQuery
        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: false, nullsFirst: false })
        .order(COLUMNS.BASE_PRICE, { ascending: false, nullsFirst: false });
    }
  } else {
    productsQuery = productsQuery.order(sortColumn, {
      ascending: sortAscending,
    });
  }

  // Add pagination
  productsQuery = productsQuery.range(offset, offset + limit - 1);

  return await productsQuery;
};

// Helper function to get the column name for sorting
export function getSortingColumn(
  sortBy: BusinessSortBy | string,
  isProductView: boolean = false
): string {
  // For product view, we need to handle sorting differently
  if (isProductView) {
    switch (sortBy) {
      case "name_asc":
      case "name_desc":
        return "name";
      case "price_asc":
      case "price_desc":
        // We'll handle price sorting with a custom approach in the query
        return "price";
      case "newest":
        // Handle 'newest' as a special case - sort by created_at descending
        return "created_at";
      case "created_asc":
      case "created_desc":
      default:
        return "created_at";
    }
  } else {
    // For business view
    switch (sortBy) {
      case "name_asc":
      case "name_desc":
        return "name";
      case "price_asc":
      case "price_desc":
        return "base_price";
      case "likes_desc":
        return "likes_count";
      case "subscriptions_desc":
        return "subscriptions_count";
      case "rating_desc":
        return "average_rating";
      case "created_asc":
      case "created_desc":
      default:
        return "created_at";
    }
  }
}

// Helper function to determine sort direction
export function getSortingDirection(sortBy: BusinessSortBy | string): boolean {
  switch (sortBy) {
    case "name_asc":
    case "price_asc":
    case "created_asc":
      return true; // ascending
    case "name_desc":
    case "price_desc":
    case "likes_desc":
    case "subscriptions_desc":
    case "rating_desc":
    case "created_desc":
    case "newest": // 'newest' is equivalent to 'created_desc'
    default:
      return false; // descending
  }
}

/**
 * Fetches business products for public card view.
 * @param businessId The ID of the business.
 * @param limit The maximum number of products to return.
 * @returns Promise<{ success: boolean; data?: Tables<"products_services">[]; error?: string }>
 */
export async function fetchBusinessProductsForCard(
  businessId: string,
  limit: number = 6
): Promise<{ success: boolean; data?: Tables<"products_services">[]; error?: string }> {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select("*")
      .eq(COLUMNS.BUSINESS_ID, businessId)
      .eq(COLUMNS.IS_AVAILABLE, true)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching business products:", error);
      return { success: false, error: "Failed to fetch products" };
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Exception fetching business products:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}