import "expo-dev-client"; // Add error handling for development builds
import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider as NavigationThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from "react-native-reanimated";
// SafeAreaProvider is automatically provided by Expo Router
// Removed to prevent nested SafeAreaProvider conflicts
import { AuthGuard } from "@/src/components/AuthGuard";
import { ToastProvider } from "@/src/components/ui/Toast";
import { StatusBarManager } from "@/src/components/layout/StatusBarManager";
import { AuthProvider } from "@/src/contexts/AuthContext";
import { ThemeProvider, useColorScheme } from "@/src/contexts/ThemeContext";
import { NotificationProvider } from "@/src/contexts/NotificationContext";
import { LocationProvider } from "@/src/contexts/LocationContext";
// Push notification provider removed
import { useEffect, useState } from "react";
import { configureGoogleAuth } from "@/src/config/supabase/services/authService";
import { useDynamicSafeArea } from "@/src/hooks/useDynamicSafeArea";
import { ErrorBoundary } from "@/src/components/ErrorBoundary";
import { productionErrorLogger } from "@/backend/supabase/services/monitoring/productionErrorLogging";
import { SplashScreen as CustomSplashScreen } from "@/src/components/ui/SplashScreen";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";

// Prevent the splash screen from auto-hiding before we show our custom splash
SplashScreen.preventAutoHideAsync();

// Configure Reanimated logger to disable strict mode warnings
configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false, // Disable strict mode to reduce warnings
  // Alternative: Keep strict mode but reduce verbosity
  // strict: true,
  // level: ReanimatedLogLevel.error, // Only show errors, not warnings
});

function AppContent() {
  const colorScheme = useColorScheme();
  const [showCustomSplash, setShowCustomSplash] = useState(true);
  const [servicesInitialized, setServicesInitialized] = useState(false);

  // Initialize dynamic safe area handling
  useDynamicSafeArea();

  // Initialize services on app startup
  useEffect(() => {
    const initializeServices = async () => {
      // Note: Supabase client is now initialized synchronously following official pattern

      try {
        // Initialize Google Auth
        await configureGoogleAuth();
        // Google Auth configured successfully
      } catch (error) {
        // Google Auth configuration skipped - this is expected if Google Sign-In package is not available
        // The app will fall back to web-based Google auth
      }

      try {
        // Initialize production error logging
        await productionErrorLogger.initialize();
      } catch (error) {
        // Silently fail if error logging initialization fails
      }

      setServicesInitialized(true);
    };

    initializeServices();
  }, []);

  // Show custom splash screen first, before any providers or auth logic
  if (showCustomSplash) {
    return (
      <CustomSplashScreen
        onAnimationComplete={() => setShowCustomSplash(false)}
        duration={2000}
      />
    );
  }

  // Only after splash screen completes, show the main app with all providers
  // SafeAreaProvider is automatically provided by Expo Router, no need to wrap again
  return (
    <>
      <StatusBarManager />
      <AuthProvider>
        <LocationProvider>
          <NotificationProvider>
            <ToastProvider>
              <NavigationThemeProvider
                value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
              >
                <AuthGuard>
                  <Stack>
                    <Stack.Screen
                      name="index"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="(auth)"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="(onboarding)"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="(dashboard)"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="(tabs)"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="business/[businessSlug]"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="product/[productId]"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen name="+not-found" />
                  </Stack>
                </AuthGuard>
              </NavigationThemeProvider>
            </ToastProvider>
          </NotificationProvider>
        </LocationProvider>
      </AuthProvider>
    </>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  // Hide the default Expo splash screen once fonts are loaded
  useEffect(() => {
    if (loaded) {
      // Small delay to ensure smooth transition to custom splash
      setTimeout(() => {
        SplashScreen.hideAsync();
      }, 100);
    }
  }, [loaded]);

  if (!loaded) {
    // Keep showing native splash while fonts load
    return null;
  }

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <ThemeProvider>
          <BottomSheetModalProvider>
            <AppContent />
          </BottomSheetModalProvider>
        </ThemeProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}
