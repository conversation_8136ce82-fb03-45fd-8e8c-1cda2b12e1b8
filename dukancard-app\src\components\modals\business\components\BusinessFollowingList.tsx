
import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { fetchBusinessFollowing, BusinessFollowingWithProfile } from "@/backend/supabase/services/business/businessSocialService";
import { useAuth } from "@/src/contexts/AuthContext";
import { createFollowingModalStyles } from "@/styles/modals/customer/following-modal";


interface BusinessFollowingListProps {
  businessId: string;
  searchTerm: string;
  followingType: "followers" | "following";
}

const BusinessFollowingList: React.FC<BusinessFollowingListProps> = ({
  businessId,
  searchTerm,
  followingType,
}) => {
  const theme = useTheme();
  const styles = createFollowingModalStyles(theme);
  const typedStyles: ReturnType<typeof createFollowingModalStyles> = styles;
  const { user } = useAuth();
  const [follows, setFollows] = useState<BusinessFollowingWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchFollows = useCallback(async () => {
    if (!hasMore || loading) return;

    setLoading(true);
    try {
      const result = await fetchBusinessFollowing(
        businessId,
        page,
        20,
        searchTerm
      );

      const { items, hasMore: newHasMore } = result;

      setFollows((prev) => (page === 1 ? items : [...prev, ...items]));
      setHasMore(newHasMore);
    } catch (error) {
      console.error("Error fetching follows:", error);
    } finally {
      setLoading(false);
    }
  }, [businessId, searchTerm, followingType, page, hasMore, loading, user?.id]);

  useEffect(() => {
    setFollows([]);
    setPage(1);
    setHasMore(true);
  }, [searchTerm, followingType]);

  useEffect(() => {
    fetchFollows();
  }, [fetchFollows]);

  const renderItem = ({ item }: { item: any }) => (
    <View style={typedStyles.listItemContainer}>
      <Text style={typedStyles.listItemText }>
        {followingType === "followers"
          ? item.user.name
          : item.business.name}
      </Text>
    </View>
  );

  const renderFooter = () => {
    if (!loading) return null;
    return <ActivityIndicator style={{ marginVertical: 20 }} />;
  };

  const handleLoadMore = () => {
    if (hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  return (
    <FlatList
      data={follows}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      ListFooterComponent={renderFooter}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      showsVerticalScrollIndicator={false}
    />
  );
};

export default BusinessFollowingList;
