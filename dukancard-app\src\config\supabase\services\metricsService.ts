import { supabase } from "@/src/config/supabase";
import { TABLES, COLUMNS, RPC_FUNCTIONS, RPC_PARAMS } from "../constants";
import { BusinessMetrics, VisitAnalyticsData } from "@/src/types/business/analytics";

export interface ActivityMetrics {
  likesCount: number;
  reviewCount: number;
  subscriptionCount: number;
  lastUpdated: string;
}

/**
 * Get customer activity metrics with caching support.
 * @param userId The ID of the user.
 * @returns Promise<ActivityMetrics | null> - The activity metrics or null if an error occurs.
 */
export async function getActivityMetrics(
  userId: string
): Promise<ActivityMetrics | null> {
  try {
    // Try to get cached metrics first if useCache is true

    // Fetch fresh metrics from database
    const [likesResult, reviewsResult, subscriptionsResult] = await Promise.all(
      [
        supabase
          .from(TABLES.LIKES)
          .select(COLUMNS.ID, { count: "exact" })
          .eq(COLUMNS.USER_ID, userId),
        supabase
          .from(TABLES.RATINGS_REVIEWS)
          .select(COLUMNS.ID, { count: "exact" })
          .eq(COLUMNS.USER_ID, userId),
        supabase
          .from(TABLES.SUBSCRIPTIONS)
          .select(COLUMNS.ID, { count: "exact" })
          .eq(COLUMNS.USER_ID, userId),
      ]
    );

    const metrics: ActivityMetrics = {
      likesCount: likesResult.count || 0,
      reviewCount: reviewsResult.count || 0,
      subscriptionCount: subscriptionsResult.count || 0,
      lastUpdated: new Date().toISOString(),
    };

    // Cache the fresh metrics

    return metrics;
  } catch (error) {
    console.error("Error fetching activity metrics:", error);

    return null;
  }
}

/**
 * Fetches business profile metrics (likes, subscriptions, rating, total visits).
 * @param businessId The ID of the business.
 * @returns Promise<{ data: { total_likes: number; total_subscriptions: number; average_rating: number; total_visits: number; } | null; error: Error | null }>
 */
export async function fetchBusinessProfileMetrics(businessId: string) {
  return await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(`${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.TOTAL_VISITS}`)
    .eq(COLUMNS.ID, businessId)
    .single();
}

/**
 * Counts products for a given business.
 * @param businessId The ID of the business.
 * @returns Promise<{ count: number | null; error: Error | null }>
 */
export async function countBusinessProducts(businessId: string) {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(COLUMNS.ID, { count: "exact" })
    .eq(COLUMNS.BUSINESS_ID, businessId);
}

/**
 * Calls the 'get_monthly_unique_visits' RPC function.
 * @param businessId The ID of the business.
 * @param targetYear The target year.
 * @param targetMonth The target month.
 * @returns Promise<{ data: number | null; error: Error | null }>
 */
export async function getMonthlyUniqueVisits(
  businessId: string,
  targetYear: number,
  targetMonth: number
) {
  return await supabase.rpc(RPC_FUNCTIONS.GET_MONTHLY_UNIQUE_VISITS, {
    [RPC_PARAMS.BUSINESS_ID]: businessId,
    [RPC_PARAMS.TARGET_YEAR]: targetYear,
    [RPC_PARAMS.TARGET_MONTH]: targetMonth,
  });
}

/**
 * Calls the 'get_daily_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param startDate The start date for the trend.
 * @param endDate The end date for the trend.
 * @returns Promise<{ data: { date: string; visits: number }[] | null; error: Error | null }>
 */
export async function getDailyUniqueVisitTrend(
  businessId: string,
  startDate: string,
  endDate: string
) {
  return await supabase.rpc(RPC_FUNCTIONS.GET_DAILY_UNIQUE_VISIT_TREND, {
    [RPC_PARAMS.BUSINESS_ID]: businessId,
    [RPC_PARAMS.START_DATE]: startDate,
    [RPC_PARAMS.END_DATE]: endDate,
  });
}

/**
 * Calls the 'get_hourly_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param targetDate The target date for the hourly trend.
 * @returns Promise<{ data: { hour: number; visits: number }[] | null; error: Error | null }>
 */
export async function getHourlyUniqueVisitTrend(
  businessId: string,
  targetDate: string
) {
  return await supabase.rpc(RPC_FUNCTIONS.GET_HOURLY_UNIQUE_VISIT_TREND, {
    [RPC_PARAMS.BUSINESS_ID]: businessId,
    [RPC_PARAMS.TARGET_DATE]: targetDate,
  });
}

/**
 * Calls the 'get_available_years_for_monthly_metrics' RPC function.
 * @param businessId The ID of the business.
 * @returns Promise<{ data: { year: number }[] | null; error: Error | null }>
 */
export async function getAvailableYearsForMonthlyMetrics(businessId: string) {
  return await supabase.rpc(RPC_FUNCTIONS.GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS, {
    [RPC_PARAMS.BUSINESS_ID]: businessId,
  });
}

/**
 * Calls the 'get_total_unique_visits' RPC function.
 * @param businessId The ID of the business.
 * @returns Promise<{ data: number | null; error: Error | null }>
 */
export async function getTotalUniqueVisits(businessId: string) {
  return await supabase.rpc(RPC_FUNCTIONS.GET_TOTAL_UNIQUE_VISITS, {
    [RPC_PARAMS.BUSINESS_ID]: businessId,
  });
}