/**
 * Centralized Discovery Service
 * Handles all search, filter, and sort operations for the discovery screen
 * Eliminates redundancies and provides consistent behavior
 */

import {
  BusinessSortBy,
  BusinessCardData,
  NearbyProduct,
  ProductSortOption,
} from "@/types/discovery";
import { fetchOnlineBusinessProfiles, countOnlineBusinessProfiles, fetchOnlineBusinessIds, applySorting } from "@/config/supabase/services/businessProfileService";
import { countProducts, fetchProductsWithFilters } from "@/config/supabase/services/productService";

// Types
export interface DiscoverySearchParams {
  viewType: "cards" | "products";
  searchTerm?: string | null;
  category?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  page?: number;
  limit?: number;
  businessSort?: BusinessSortBy;
  productSort?: ProductSortOption;
  productType?: "physical" | "service" | null;
  userLocation?: { latitude: number; longitude: number };
}

export interface DiscoveryResult {
  businesses?: BusinessCardData[];
  products?: NearbyProduct[];
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
  isAuthenticated: boolean;
  location?: { city: string; state: string } | null;
}

export interface NormalizedParams {
  viewType: "cards" | "products";
  searchTerm: string | null;
  category: string | null;
  pincode: string | null;
  city: string | null;
  locality: string | null;
  page: number;
  limit: number;
  businessSort: BusinessSortBy;
  productSort: ProductSortOption;
  productType: "physical" | "service" | null;
}

/**
 * Centralized Discovery Service Class
 */
export class DiscoveryService {
  

  /**
   * Main search method - handles all discovery operations
   */
  async search(params: DiscoverySearchParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      // Validate and normalize parameters
      const normalizedParams = this.validateAndNormalizeParams(params);

      this.logOperation("search", normalizedParams);

      // Route to appropriate search method based on view type
      if (normalizedParams.viewType === "products") {
        return await this.searchProducts(normalizedParams);
      } else {
        return await this.searchBusinesses(normalizedParams);
      }
    } catch (error) {
      console.error("DiscoveryService.search error:", error);
      return { error: "An unexpected error occurred during search" };
    }
  }

  /**
   * Search for products
   */
  private async searchProducts(params: NormalizedParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      const offset = (params.page - 1) * params.limit;

      // Step 1: Get valid business IDs based on filters
      const validBusinessIds = await this.getValidBusinessIds(params);

      if (validBusinessIds.length === 0) {
        return {
          data: {
            products: [],
            totalCount: 0,
            hasMore: false,
            nextPage: null,
            isAuthenticated: true,
          },
        };
      }

      // Step 2: Count total products
      const totalCount = await this.countProducts(validBusinessIds, params);

      // Step 3: Fetch products
      const products = await this.fetchProducts(
        validBusinessIds,
        params,
        offset
      );

      // Step 4: Calculate pagination
      const hasMore = totalCount > offset + products.length;
      const nextPage = hasMore ? params.page + 1 : null;

      return {
        data: {
          products,
          totalCount,
          hasMore,
          nextPage,
          isAuthenticated: true,
        },
      };
    } catch (error) {
      console.error("DiscoveryService.searchProducts error:", error);
      return { error: "Failed to search products" };
    }
  }

  /**
   * Search for businesses
   */
  private async searchBusinesses(params: NormalizedParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      const offset = (params.page - 1) * params.limit;

      const { count, error: countError } = await countOnlineBusinessProfiles(
        params.city,
        params.pincode,
        params.locality,
        params.category,
        params.searchTerm
      );

      if (countError) {
        console.error("Business count query error:", countError);
        return { error: "Failed to count businesses" };
      }

      const { data: businessesData, error: dataError } = await fetchOnlineBusinessProfiles(
        params.city,
        params.pincode,
        params.locality,
        params.category,
        params.searchTerm,
        params.businessSort,
        offset,
        params.limit
      );

      if (dataError) {
        console.error("Business data query error:", dataError);
        return { error: "Failed to fetch businesses" };
      }

      // Process results
      const totalCount = count || 0;
      const businesses = this.processBusinessData(businessesData || []);
      const hasMore = totalCount > offset + businesses.length;
      const nextPage = hasMore ? params.page + 1 : null;

      return {
        data: {
          businesses,
          totalCount,
          hasMore,
          nextPage,
          isAuthenticated: true,
        },
      };
    } catch (error) {
      console.error("DiscoveryService.searchBusinesses error:", error);
      return { error: "Failed to search businesses" };
    }
  }

  /**
   * Get valid business IDs based on location and category filters
   */
  private async getValidBusinessIds(
    params: NormalizedParams
  ): Promise<string[]> {
    console.log("🔍 getValidBusinessIds called with filters:", {
      city: params.city,
      pincode: params.pincode,
      locality: params.locality,
      category: params.category,
    });

    const { data, error } = await fetchOnlineBusinessIds(
      params.city,
      params.pincode,
      params.locality,
      params.category
    );

    if (error) {
      console.error("❌ Error fetching valid business IDs:", error);
      return [];
    }

    const businessIds = data?.map((b) => b.id) || [];
    console.log("✅ Found valid business IDs:", {
      count: businessIds.length,
      ids: businessIds.slice(0, 5), // Log first 5 IDs for debugging
    });

    return businessIds;
  }

  /**
   * Count total products from valid businesses
   */
  private async countProducts(
    validBusinessIds: string[],
    params: NormalizedParams
  ): Promise<number> {
    const { count, error } = await countProducts(
      validBusinessIds,
      params.productType,
      params.searchTerm
    );

    if (error) {
      console.error("Error counting products:", error);
      return 0;
    }

    return count || 0;
  }

  /**
   * Fetch products from valid businesses
   */
  private async fetchProducts(
    validBusinessIds: string[],
    params: NormalizedParams,
    offset: number
  ): Promise<NearbyProduct[]> {
    const { data, error } = await fetchProductsWithFilters(
      validBusinessIds,
      params.productType,
      params.searchTerm,
      params.productSort,
      offset,
      params.limit
    );

    if (error) {
      console.error("Error fetching products:", error);
      return [];
    }

    return this.processProductData(data || []);
  }

  

  /**
   * Process raw business data into BusinessCardData format
   */
  private processBusinessData(rawData: any[]): BusinessCardData[] {
    return rawData.map((data) => ({
      id: data.id,
      business_name: data.business_name ?? "",
      contact_email: data.contact_email ?? "",
      has_active_subscription: true, // Simplified for React Native
      trial_end_date: data.trial_end_date ?? null,
      created_at: data.created_at ?? undefined,
      updated_at: data.updated_at ?? undefined,
      logo_url: data.logo_url ?? "",
      member_name: data.member_name ?? "",
      title: data.title ?? "",
      address_line: data.address_line ?? "",
      city: data.city ?? "",
      state: data.state ?? "",
      pincode: data.pincode ?? "",
      locality: data.locality ?? "",
      phone: data.phone ?? "",
      instagram_url: data.instagram_url ?? "",
      facebook_url: data.facebook_url ?? "",
      whatsapp_number: data.whatsapp_number ?? "",
      about_bio: data.about_bio ?? "",
      status: data.status ?? "online",
      business_slug: data.business_slug ?? "",
      theme_color: data.theme_color ?? "",
      delivery_info: data.delivery_info ?? "",
      business_hours: data.business_hours ?? "",
      business_category: data.business_category ?? "",
      google_maps_url: null, // Not used anymore
      total_likes: data.total_likes ?? 0,
      total_subscriptions: data.total_subscriptions ?? 0,
      average_rating: data.average_rating ?? 0,
      established_year: data.established_year ?? null,
      website_url: "",
      linkedin_url: "",
      twitter_url: "",
      youtube_url: "",
      call_number: "",
      total_visits: 0,
      today_visits: 0,
      yesterday_visits: 0,
      visits_7_days: 0,
      visits_30_days: 0,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      gallery: null,
      latitude: data.latitude ?? null,
      longitude: data.longitude ?? null,
      custom_branding: data.custom_branding ?? null,
      custom_ads: data.custom_ads ?? null,
    }));
  }

  /**
   * Process raw product data into NearbyProduct format
   */
  private processProductData(rawData: any[]): NearbyProduct[] {
    return rawData.map((product) => ({
      id: product.id,
      business_id: product.business_id,
      name: product.name || "",
      description: product.description,
      base_price: product.base_price,
      discounted_price: product.discounted_price,
      product_type: product.product_type,
      is_available: product.is_available,
      image_url: product.image_url,
      created_at: product.created_at,
      updated_at: product.updated_at,
      slug: product.slug,
      images: product.images || null,
      featured_image_index: product.featured_image_index || null,
      business_slug: product.business_profiles?.business_slug || "",
      businessLatitude: product.business_profiles?.latitude || null,
      businessLongitude: product.business_profiles?.longitude || null,
    }));
  }

  /**
   * Validate and normalize input parameters
   */
  private validateAndNormalizeParams(
    params: DiscoverySearchParams
  ): NormalizedParams {
    return {
      viewType: params.viewType || "products",
      searchTerm: this.normalizeString(params.searchTerm),
      category: this.normalizeString(params.category),
      pincode: this.normalizeString(params.pincode),
      city: this.normalizeString(params.city),
      locality: this.normalizeString(params.locality),
      page: Math.max(1, params.page || 1),
      limit: Math.min(50, Math.max(1, params.limit || 20)),
      businessSort: params.businessSort || "created_desc",
      productSort: params.productSort || "newest",
      productType: params.productType || null,
    };
  }

  /**
   * Normalize string parameters
   */
  private normalizeString(value: string | null | undefined): string | null {
    if (!value || typeof value !== "string") return null;
    const trimmed = value.trim();
    return trimmed.length > 0 ? trimmed : null;
  }

  /**
   * Log operations for debugging
   */
  private logOperation(operation: string, params: any): void {
    console.log(`DiscoveryService.${operation}:`, {
      viewType: params.viewType,
      searchTerm: params.searchTerm,
      category: params.category,
      location: {
        city: params.city,
        pincode: params.pincode,
        locality: params.locality,
      },
      pagination: {
        page: params.page,
        limit: params.limit,
      },
      sorting: {
        businessSort: params.businessSort,
        productSort: params.productSort,
      },
    });
  }
}

// Export singleton instance
export const discoveryService = new DiscoveryService();
