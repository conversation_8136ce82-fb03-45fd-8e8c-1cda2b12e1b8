
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import LoginScreen from '@/app/(auth)/login';
import { AuthProvider } from '@/src/contexts/AuthContext';
import { useAuth } from '@/src/contexts/AuthContext';

jest.mock('@/src/contexts/AuthContext', () => ({
  ...jest.requireActual('@/src/contexts/AuthContext'),
  useAuth: jest.fn(),
}));
import { router } from 'expo-router';
import * as emailOtpService from '@/backend/supabase/services/auth/emailOtpService';
import * as authService from '@/src/config/supabase/services/authService';
import { MobileAuthService } from '@/backend/supabase/services/auth/mobileAuthService';
import { useAuthErrorHandler } from '@/src/hooks/useAuthErrorHandler';

// Mocking services and navigation
jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
}));

jest.mock('@/src/hooks/useAuthErrorHandler', () => ({
  useAuthErrorHandler: jest.fn(() => ({
    executeWithErrorHandling: jest.fn(async (config) => {
      try {
        const result = await config.operation();
        if (config.onSuccess) {
          await config.onSuccess(result);
        }
        return result;
      } catch (error) {
        if (config.onError) {
          await config.onError(error);
        }
        throw error; // Re-throw to allow test to catch errors
      }
    }),
    clearError: jest.fn(),
    isOnline: true,
    error: null,
  })),
}));

jest.mock('@/backend/supabase/services/auth/emailOtpService');
jest.mock('@/src/config/supabase/services/authService');
jest.mock('@/backend/supabase/services/auth/mobileAuthService');

// Mocking UI components that cause issues in Jest
jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
  }),
}));

jest.mock('expo-blur', () => {
  const React = require('react');
  const ReactNative = require('react-native');
  return {
    BlurView: ({ children }: { children: React.ReactNode }) => React.createElement(ReactNative.View, null, children),
  };
});

const renderWithProviders = (ui: React.ReactElement) => {
  return render(<AuthProvider>{ui}</AuthProvider>);
};

describe('Login Screen Integration Test', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Mock the checkUserRole function from AuthContext to simulate a successful login outcome
    const useAuthMock = useAuth as jest.Mock;
    useAuthMock.mockReturnValue({
      checkUserRole: jest.fn().mockResolvedValue({
        needsRoleSelection: false,
        needsOnboarding: false,
        role: 'customer',
      }),
    });
  });

  test('should complete the email OTP flow and navigate to the dashboard', async () => {
    // Arrange: Mock service responses
    const sendEmailOTPMock = jest.spyOn(emailOtpService, 'sendEmailOTP').mockResolvedValue({ success: true, message: 'OTP sent' });
    const verifyEmailOTPMock = jest.spyOn(emailOtpService, 'verifyEmailOTP').mockResolvedValue({ success: true, message: 'Verified' });
    const validateEmailMock = jest.spyOn(emailOtpService, 'validateEmail').mockReturnValue({ isValid: true });
    const validateOTPMock = jest.spyOn(emailOtpService, 'validateOTP').mockReturnValue({ isValid: true });

    // Act: Render the Login screen
    const { getByPlaceholderText, getByText, findByText } = renderWithProviders(<LoginScreen />);

    // 1. Enter email and submit
    fireEvent.changeText(getByPlaceholderText('Enter your email address'), '<EMAIL>');
    fireEvent.press(getByText('Continue'));

    // Assert: Check if OTP was sent
    await waitFor(() => {
      expect(sendEmailOTPMock).toHaveBeenCalledWith('<EMAIL>');
    });

    // 2. Wait for OTP screen and enter OTP
    const otpInput = await findByText('Verify Your Email');
    expect(otpInput).toBeTruthy();

    // The OTPInput component is a custom one. We'll find the underlying TextInput.
    // This is a bit brittle and depends on implementation, but necessary for integration.
    // A better approach would be to add a testID to the OTPInput's TextInput.
    const otpTextInput = getByPlaceholderText('------'); // Assuming this placeholder exists from the component's code
    fireEvent.changeText(otpTextInput, '123456');

    // Assert: Check if OTP was verified and navigation occurred
    await waitFor(() => {
      expect(verifyEmailOTPMock).toHaveBeenCalledWith('<EMAIL>', '123456');
      expect(router.replace).toHaveBeenCalledWith('/(dashboard)/customer');
    });
  });
});
