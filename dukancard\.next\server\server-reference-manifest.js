self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00089e22292962faf3dc20145ff66dcc48cc14156d\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"rsc\"\n      }\n    },\n    \"006dd2da128ed0928b5dfe57d5e39b7acfa1e4a058\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"rsc\"\n      }\n    },\n    \"00c2d9624329cded09d08193b88cb56d91c74b75b9\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"rsc\"\n      }\n    },\n    \"4099bda4cdd529b888f7cedd4915015131201e8e46\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"action-browser\"\n      }\n    },\n    \"40ab1879cfbb4ab5aaf23c401e255af71aeeab13a9\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"1TCz2iHGXTF9z/Vm+N6MDej5HZtAZG+srmqCH3mHLPg=\"\n}"