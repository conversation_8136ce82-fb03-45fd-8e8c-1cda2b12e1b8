import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StyleProp,
  ViewStyle,
  TextStyle,
} from "react-native";
import { fetchLikes, unlikeBusiness as unlike, LikeWithProfile } from "@/src/config/supabase/services/likesService";
import { useAuth } from "@/src/contexts/AuthContext";
import { LikeCard } from "@/src/components/social/LikeCard";
import { LikesModalSkeleton } from "@/src/components/skeletons/modals/LikesModalSkeleton";
import { createLikesModalStyles } from "@/styles/modals/customer/likes-modal";
import { useTheme } from "@/src/hooks/useTheme";
import { SearchComponent } from "@/src/components/social/SearchComponent";

interface LikesListProps {
  searchTerm: string;
}

const LikesList: React.FC<LikesListProps> = ({ searchTerm }) => {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createLikesModalStyles(theme);

  const [likes, setLikes] = useState<LikeWithProfile[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const loadLikes = useCallback(
    async (isRefreshing = false) => {
      if (!user) return;

      if (isRefreshing) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      try {
        const result = await fetchLikes(
          user.id,
          isRefreshing ? 1 : page,
          20,
          searchTerm
        );
        if (isRefreshing) {
          setLikes(result.items);
        } else {
          setLikes((prev) =>
            page === 1 ? result.items : [...prev, ...result.items]
          );
        }
        setHasMore(result.hasMore);
      } catch (error) {
        console.error("Failed to fetch likes:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
        setRefreshing(false);
      }
    },
    [user, page, searchTerm]
  );

  useEffect(() => {
    setPage(1);
    loadLikes(true);
  }, [searchTerm, loadLikes]);

  useEffect(() => {
    if (page > 1) {
      loadLikes();
    }
  }, [page, loadLikes]);

  const handleRefresh = () => {
    setPage(1);
    loadLikes(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  const handleUnlike = async (likeId: string) => {
    try {
      await unlike(likeId);
      setLikes((prevLikes) => prevLikes.filter((like) => like.id !== likeId));
    } catch (error) {
      console.error("Failed to unlike:", error);
    }
  };

  if (loading) {
    return <LikesModalSkeleton />;
  }

  if (likes.length === 0) {
    return (
      <View style={styles.emptyContainer }>
        <Text style={styles.emptyText }>No liked businesses found.</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={likes}
      renderItem={({ item }) => (
        <LikeCard like={item} onUnlike={handleUnlike} />
      )}
      keyExtractor={(item) => item.id}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={
        loadingMore ? (
          <ActivityIndicator style={styles.footerLoadingContainer} />
        ) : null
      }
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    />
  );
};

export default LikesList;
