import { BusinessMetrics, VisitAnalyticsData } from "@/types/business/analytics";
import { fetchBusinessProfileMetrics, countBusinessProducts, getMonthlyUniqueVisits, getDailyUniqueVisitTrend, getHourlyUniqueVisitTrend, getAvailableYearsForMonthlyMetrics, getTotalUniqueVisits } from "@/src/config/supabase/services/metricsService";

class BusinessAnalyticsService {
  private getKolkataDateString = (offsetDays: number = 0): string => {
    const now = new Date();
    const istOffset = 5.5 * 60 * 60 * 1000;
    const istDate = new Date(now.getTime() + istOffset);
    istDate.setUTCDate(istDate.getUTCDate() - offsetDays);
    const year = istDate.getUTCFullYear();
    const month = String(istDate.getUTCMonth() + 1).padStart(2, "0");
    const day = String(istDate.getUTCDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  async getBusinessMetrics(
    businessId: string
  ): Promise<{ data?: BusinessMetrics; error?: string }> {
    const { data, error } = await fetchBusinessProfileMetrics(businessId);

    if (error) {
      return { error: "Failed to fetch business metrics." };
    }

    const { count: total_products, error: productsError } = await countBusinessProducts(businessId);

    if (productsError) {
      return { error: "Failed to fetch business products." };
    }

    const now = new Date();
    const istOffset = 5.5 * 60 * 60 * 1000;
    const istDate = new Date(now.getTime() + istOffset);
    const currentYear = istDate.getUTCFullYear();
    const currentMonth = istDate.getUTCMonth() + 1;

    const { data: monthly_visits, error: monthlyVisitsError } =
      await getMonthlyUniqueVisits(businessId, currentYear, currentMonth);

    if (monthlyVisitsError) {
      return { error: "Failed to fetch monthly visits." };
    }

    return {
      data: {
        total_likes: data.total_likes ?? 0,
        total_subscriptions: data.total_subscriptions ?? 0,
        average_rating: data.average_rating ?? 0,
        total_products: total_products ?? 0,
        total_views: data.total_visits ?? 0,
        monthly_visits: monthly_visits ?? 0,
      },
    };
  }

  async getVisitAnalytics(
    businessId: string,
    userPlan?: string | null
  ): Promise<{ data?: VisitAnalyticsData; error?: string }> {
    const isPremiumUser =
      userPlan === "growth" || userPlan === "pro" || userPlan === "enterprise";

    try {
      const { data: profile, error: profileError } = await fetchBusinessProfileMetrics(businessId);

      const todayStr = this.getKolkataDateString(0);
      const yesterdayStr = this.getKolkataDateString(1);
      const sevenDaysAgoStr = this.getKolkataDateString(7);
      const thirtyDaysAgoStr = this.getKolkataDateString(30);

      const now = new Date();
      const istOffset = 5.5 * 60 * 60 * 1000;
      const istDate = new Date(now.getTime() + istOffset);
      const currentYear = istDate.getUTCFullYear();
      const currentMonth = istDate.getUTCMonth() + 1;

      let previousMonth = currentMonth - 1;
      let previousYear = currentYear;
      if (previousMonth === 0) {
        previousMonth = 12;
        previousYear = currentYear - 1;
      }

      let trend7Data: { date: string; visits: number }[] = [];
      let trend30Data: { date: string; visits: number }[] = [];
      let hourlyTrendData: { hour: number; visits: number }[] = [];

      if (isPremiumUser) {
        const { data: trend7Result, error: trend7Error } = await getDailyUniqueVisitTrend(
          businessId,
          sevenDaysAgoStr,
          todayStr
        );
        if (trend7Error)
          throw new Error(
            `Failed to fetch 7-day trend: ${trend7Error.message}`
          );
        trend7Data = trend7Result;

        const { data: trend30Result, error: trend30Error } = await getDailyUniqueVisitTrend(
          businessId,
          thirtyDaysAgoStr,
          todayStr
        );
        if (trend30Error)
          throw new Error(
            `Failed to fetch 30-day trend: ${trend30Error.message}`
          );
        trend30Data = trend30Result;

        const { data: hourlyTrendResult, error: hourlyTrendError } =
          await getHourlyUniqueVisitTrend(businessId, todayStr);
        if (hourlyTrendError)
          throw new Error(
            `Failed to fetch hourly trend: ${hourlyTrendError.message}`
          );
        hourlyTrendData = hourlyTrendResult;
      }

      const { data: currentMonthData, error: currentMonthError } =
        await getMonthlyUniqueVisits(businessId, currentYear, currentMonth);
      if (currentMonthError)
        throw new Error(
          `Failed to fetch current month visits: ${currentMonthError.message}`
        );

      const { data: previousMonthData, error: previousMonthError } =
        await getMonthlyUniqueVisits(businessId, previousYear, previousMonth);
      if (previousMonthError)
        throw new Error(
          `Failed to fetch previous month visits: ${previousMonthError.message}`
        );

      let availableYears: number[] = [currentYear];
      let monthlyTrendData: { year: number; month: number; visits: number }[] =
        [];

      if (isPremiumUser) {
        const { data: availableYearsData, error: availableYearsError } =
          await getAvailableYearsForMonthlyMetrics(businessId);
        if (availableYearsError)
          throw new Error(
            `Failed to fetch available years: ${availableYearsError.message}`
          );
        if (
          availableYearsData &&
          Array.isArray(availableYearsData) &&
          availableYearsData.length > 0
        ) {
          availableYears = availableYearsData.map((item) => item.year);
        } else {
          availableYears = [currentYear];
        }

        const { data: monthlyTrendResult, error: monthlyTrendError } =
          await getMonthlyUniqueVisitTrend(
            businessId,
            Math.min(...availableYears),
            1,
            currentYear,
            currentMonth
          );
        if (monthlyTrendError)
          throw new Error(
            `Failed to fetch monthly trend: ${monthlyTrendError.message}`
          );
        monthlyTrendData = monthlyTrendResult;
      }

      let totalVisits = profile?.total_visits ?? 0;
      let todayVisits = profile?.today_visits ?? 0;
      let yesterdayVisits = profile?.yesterday_visits ?? 0;
      let visits7Days = profile?.visits_7_days ?? 0;
      let visits30Days = profile?.visits_30_days ?? 0;

      if (!profile || profileError) {
        todayVisits =
          trend7Data?.find(
            (d: { date: string; visits: number }) => d.date === todayStr
          )?.visits ?? 0;
        yesterdayVisits =
          trend7Data?.find(
            (d: { date: string; visits: number }) => d.date === yesterdayStr
          )?.visits ?? 0;

        const { data: totalCount, error: totalError } = await getTotalUniqueVisits(
          businessId
        );
        if (totalError)
          throw new Error(
            `Failed to fetch total visits: ${totalError.message}`
          );
        totalVisits = totalCount ?? 0;

        visits7Days =
          trend7Data?.reduce(
            (sum: number, day: { visits: number }) => sum + day.visits,
            0
          ) ?? 0;
        visits30Days =
          trend30Data?.reduce(
            (sum: number, day: { visits: number }) => sum + day.visits,
            0
          ) ?? 0;
      }

      const analyticsData: VisitAnalyticsData = {
        totalUniqueVisits: Number(totalVisits),
        todayUniqueVisits: Number(todayVisits),
        yesterdayUniqueVisits: Number(yesterdayVisits),
        visits7Days: Number(visits7Days),
        visits30Days: Number(visits30Days),
        currentMonthUniqueVisits: Number(currentMonthData || 0),
        previousMonthUniqueVisits: Number(previousMonthData || 0),
        currentYear: currentYear,
        currentMonth: currentMonth,
        dailyTrend7Days: trend7Data ?? [],
        dailyTrend30Days: trend30Data ?? [],
        hourlyTrendToday: hourlyTrendData ?? [],
        monthlyTrend: monthlyTrendData ?? [],
        availableYears: availableYears,
      };

      return { data: analyticsData };
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An unknown error occurred.";
      return { error: message };
    }
  }
}

export const businessAnalyticsService = new BusinessAnalyticsService();

