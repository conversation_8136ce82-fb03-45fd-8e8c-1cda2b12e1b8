import { supabase } from "@/src/config/supabase";
import { TABLES, COLUMNS } from "../constants";
import { Tables } from "@/src/types/supabase";

// Types for reviews
export interface ReviewBusinessProfile {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

export interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: ReviewBusinessProfile | null;
}

export interface ReviewsResult {
  items: ReviewData[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
  data?: ReviewData[];
  error?: string;
}

/**
 * Fetches user reviews with pagination and sorting.
 * @param userId The ID of the user.
 * @param page The page number to fetch.
 * @param limit The number of items per page.
 * @param sortBy The sorting criteria.
 * @param searchTerm Optional search term to filter by business name.
 * @returns Promise<ReviewsResult> - The fetched reviews, total count, and pagination info.
 */
export async function fetchReviews(
  userId: string,
  page: number = 1,
  limit: number = 20,
  sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest",
  searchTerm: string = ""
): Promise<ReviewsResult> {
  try {
    // Calculate pagination
    const offset = (page - 1) * limit;

    // Build base query with business profile join for search
    let query = supabase
      .from(TABLES.RATINGS_REVIEWS)
      .select(
        `
          ${COLUMNS.ID},
          ${COLUMNS.RATING},
          ${COLUMNS.REVIEW_TEXT},
          ${COLUMNS.CREATED_AT},
          ${COLUMNS.UPDATED_AT},
          ${COLUMNS.BUSINESS_PROFILE_ID},
          ${COLUMNS.USER_ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME},
            ${COLUMNS.BUSINESS_SLUG},
            ${COLUMNS.LOGO_URL}
          )
        `,
        { count: "exact" }
      )
      .eq(COLUMNS.USER_ID, userId);

    // Apply search filter if provided
    if (searchTerm && searchTerm.trim()) {
      query = query.ilike(
        `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
        `%${searchTerm.trim()}%`
      );
    }

    // Apply sorting
    switch (sortBy) {
      case "oldest":
        query = query.order(COLUMNS.CREATED_AT, { ascending: true });
        break;
      case "rating_high":
        query = query.order(COLUMNS.RATING, { ascending: false });
        break;
      case "rating_low":
        query = query.order(COLUMNS.RATING, { ascending: true });
        break;
      case "newest":
      default:
        query = query.order(COLUMNS.CREATED_AT, { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: reviews, count: totalCount, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch reviews: ${error.message}`);
    }

    // Transform the data (business profiles are already joined)
    const transformedReviews: ReviewData[] = (reviews || []).map((review: any) => ({
      id: review.id,
      rating: review.rating,
      review_text: review.review_text,
      created_at: review.created_at,
      updated_at: review.updated_at,
      business_profile_id: review.business_profile_id,
      user_id: review.user_id,
      business_profiles: Array.isArray(review.business_profiles)
        ? review.business_profiles[0]
        : review.business_profiles,
    }));

    const hasMore = totalCount ? totalCount > offset + limit : false;

    return {
      items: transformedReviews,
      totalCount: totalCount || 0,
      hasMore,
      currentPage: page,
    };
  } catch (error) {
    console.error("Error in fetchReviews:", error);
    throw error;
  }
}

/**
 * Deletes a review.
 * @param reviewId The ID of the review to delete.
 * @returns Promise<void>
 */
export async function deleteReview(reviewId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from(TABLES.RATINGS_REVIEWS)
      .delete()
      .eq(COLUMNS.ID, reviewId);

    if (error) {
      throw new Error(`Failed to delete review: ${error.message}`);
    }
  } catch (error) {
    console.error("Error in deleteReview:", error);
    throw error;
  }
}

/**
 * Updates a review.
 * @param reviewId The ID of the review to update.
 * @param rating The new rating.
 * @param reviewText The new review text.
 * @returns Promise<void>
 */
export async function updateReview(
  reviewId: string,
  rating: number,
  reviewText: string
): Promise<void> {
  try {
    const { error } = await supabase
      .from(TABLES.RATINGS_REVIEWS)
      .update({
        [COLUMNS.RATING]: rating,
        [COLUMNS.REVIEW_TEXT]: reviewText || null,
        [COLUMNS.UPDATED_AT]: new Date().toISOString(),
      })
      .eq(COLUMNS.ID, reviewId);

    if (error) {
      throw new Error(`Failed to update review: ${error.message}`);
    }
  } catch (error) {
    console.error("Error in updateReview:", error);
    throw error;
  }
}

/**
 * Fetches business reviews (received or given) with pagination, sorting, and search.
 * @param businessId The ID of the business (for 'received' reviews).
 * @param userId The ID of the user (for 'given' reviews).
 * @param sortBy The sorting criteria.
 * @param searchTerm Optional search term to filter by name.
 * @param reviewType 'received' or 'given'.
 * @param page The page number for pagination.
 * @param limit The number of items per page.
 * @returns Promise<ReviewsResult> - The fetched reviews, total count, and pagination info.
 */
export async function fetchBusinessReviews(
  businessId: string,
  userId: string | undefined,
  sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest",
  searchTerm: string = "",
  reviewType: "received" | "given",
  page: number = 1,
  limit: number = 20
): Promise<ReviewsResult> {
  try {
    // Calculate pagination
    const offset = (page - 1) * limit;

    // Build base query with proper joins for search
    let query = supabase.from(TABLES.RATINGS_REVIEWS).select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.RATING},
      ${COLUMNS.REVIEW_TEXT},
      ${COLUMNS.CREATED_AT},
      ${COLUMNS.UPDATED_AT},
      ${COLUMNS.BUSINESS_PROFILE_ID},
      ${COLUMNS.USER_ID},
      user:${COLUMNS.USER_ID}(${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.AVATAR_URL}),
      business:${COLUMNS.BUSINESS_ID}(${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL})
      `,
      { count: "exact" }
    );

    if (reviewType === "received") {
      query = query.eq(COLUMNS.BUSINESS_ID, businessId);
    } else {
      query = query.eq(COLUMNS.USER_ID, userId);
    }

    // Apply search filter if provided
    if (searchTerm && searchTerm.trim()) {
      if (reviewType === "received") {
        query = query.ilike(`user.${COLUMNS.NAME}`, `%${searchTerm.trim()}%`);
      } else {
        query = query.ilike(`business.${COLUMNS.BUSINESS_NAME}`, `%${searchTerm.trim()}%`);
      }
    }

    // Apply sorting
    switch (sortBy) {
      case "oldest":
        query = query.order(COLUMNS.CREATED_AT, { ascending: true });
        break;
      case "rating_high":
        query = query.order(COLUMNS.RATING, { ascending: false });
        break;
      case "rating_low":
        query = query.order(COLUMNS.RATING, { ascending: true });
        break;
      case "newest":
      default:
        query = query.order(COLUMNS.CREATED_AT, { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: reviews, count: totalCount, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch reviews: ${error.message}`);
    }

    // Transform the data
    const transformedReviews: ReviewData[] = (reviews || []).map((review: any) => ({
      id: review.id,
      rating: review.rating,
      review_text: review.review_text,
      created_at: review.created_at,
      updated_at: review.updated_at,
      business_profile_id: review.business_profile_id,
      user_id: review.user_id,
      business_profiles: review.business,
    }));

    const hasMore = totalCount ? totalCount > offset + limit : false;

    return {
      items: transformedReviews,
      totalCount: totalCount || 0,
      hasMore,
      currentPage: page,
    };
  } catch (error) {
    console.error("Error in fetchBusinessReviews:", error);
    throw error;
  }
}

/**
 * Fetch review statistics for a business
 */
export async function fetchReviewStats(businessId: string): Promise<{
  success: boolean;
  data?: { totalReviews: number; averageRating: number; ratingDistribution: { [key: number]: number }; };
  error?: string;
}> {
  try {
    const { data, error } = await supabase
      .from(TABLES.RATINGS_REVIEWS)
      .select(COLUMNS.RATING)
      .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);

    if (error) {
      console.error("Error fetching review stats:", error);
      return { success: false, error: "Failed to fetch review statistics" };
    }

    const reviews = data || [];
    const totalReviews = reviews.length;

    if (totalReviews === 0) {
      return {
        success: true,
        data: {
          totalReviews: 0,
          averageRating: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        },
      };
    }

    const averageRating =
      reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;

    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach((review) => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    return {
      success: true,
      data: {
        totalReviews,
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        ratingDistribution,
      },
    };
  } catch (error) {
    console.error("Exception fetching review stats:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}
