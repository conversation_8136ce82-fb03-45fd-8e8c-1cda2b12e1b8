import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  RefreshControl,
  StyleProp,
  ViewStyle,
  ImageStyle,
  TextStyle,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { fetchBusinessFollowers, FollowerWithProfile } from "@/backend/supabase/services/business/businessSocialService";
import { createLikesModalStyles } from "@/styles/modals/customer/likes-modal";
import { ErrorState } from "@/src/components/ui/ErrorState";
import { FollowingModalSkeleton } from "@/src/components/skeletons/modals/FollowingModalSkeleton";
import { logError, handleNetworkError } from "@/src/utils/errorHandling";

interface BusinessFollowersListProps {
  businessId: string;
  searchTerm: string;
}

export default function BusinessFollowersList({
  businessId,
  searchTerm,
}: BusinessFollowersListProps) {
  const theme = useTheme();
  const styles = createLikesModalStyles(theme);
  const [followers, setFollowers] = useState<FollowerWithProfile[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<any>(null);

  const fetchFollowers = useCallback(
    async (isRefreshing = false) => {
      if (loading) return;

      setLoading(true);
      if (isRefreshing) {
        setRefreshing(true);
      }
      setError(null);

      try {
        const currentPage = isRefreshing ? 1 : page;
        const result = await fetchBusinessFollowers(
          businessId,
          currentPage,
          10
        );
        const { items, hasMore: newHasMore } = result;

        if (isRefreshing) {
          setFollowers(items);
        } else {
          setFollowers((prev) => [...prev, ...items]);
        }
        setHasMore(newHasMore);
        if (newHasMore) {
          setPage(currentPage + 1);
        }
      } catch (err) {
        const appError = handleNetworkError(err);
        setError(appError);
        logError(appError, "BusinessFollowersList.fetchFollowers");
      } finally {
        setLoading(false);
        if (isRefreshing) {
          setRefreshing(false);
        }
      }
    },
    [businessId, loading, page]
  );

  useEffect(() => {
    fetchFollowers(true);
  }, [searchTerm, fetchFollowers]);

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      fetchFollowers();
    }
  };

  const onRefresh = () => {
    setPage(1);
    fetchFollowers(true);
  };

  const renderItem = ({ item }: { item: FollowerWithProfile }) => {
    const profile = item.profile;
    const name = profile?.name;
    const avatarUrl =
      profile?.type === "business" ? profile?.logo_url : profile?.avatar_url;

    return (
      <View style={styles.header}>
        <Image
          source={{
            uri:
              avatarUrl ||
              "https://asset.brandfetch.io/id235U50sE/idj9kF8hYy.jpeg",
          }}
          style={{ width: 50, height: 50, borderRadius: 25 } }
        />
        <View style={{ flex: 1, marginLeft: 12 }}>
          <Text style={styles.headerTitle}>{name}</Text>
          <Text style={styles.emptyText}>
            {profile?.type === "business" ? "Business" : "Customer"}
          </Text>
        </View>
      </View>
    );
  };

  const renderFooter = () => {
    if (!loading || refreshing) return null;
    return (
      <View style={{ paddingVertical: 20 }}>
        <ActivityIndicator
          animating
          size="large"
          color={theme.colors.primary}
        />
      </View>
    );
  };

  if (loading && page === 1 && !refreshing) {
    return <FollowingModalSkeleton />;
  }

  if (error && followers.length === 0) {
    return (
      <ErrorState
        title={error.title}
        message={error.message}
        onRetry={onRefresh}
      />
    );
  }

  if (!followers.length && !loading) {
    return (
      <ErrorState
        title="No Followers Found"
        message="This business doesn't have any followers yet."
      />
    );
  }

  return (
    <FlatList
      data={followers}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
      contentContainerStyle={{ paddingBottom: 20 }}
    />
  );
}
