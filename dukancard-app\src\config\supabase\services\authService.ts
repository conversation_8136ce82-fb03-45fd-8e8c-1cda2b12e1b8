import { supabase } from "@/src/config/supabase";
import { User, Session } from "@supabase/supabase-js";

import { Platform } from 'react-native';
import { getGoogleConfig } from '@/lib/config/google';

// Conditional import for native Google Sign-In
let GoogleSignin: any = null;
let statusCodes: any = null;
let isConfigured = false; // Track if Google Auth is already configured

/**
 * Initializes the Google Sign-In module.
 * This function attempts to load the Google Sign-In module for native authentication.
 * It's designed to be called once on module load or app startup.
 * @returns {boolean} True if the Google Sign-In module was successfully initialized, false otherwise.
 */
export const initializeGoogleSignIn = () => {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const googleSignInModule = require('@react-native-google-signin/google-signin');
    GoogleSignin = googleSignInModule.GoogleSignin;
    statusCodes = googleSignInModule.statusCodes;
    return true;
  } catch {
    // Native Google Sign-In not available, will fall back to web auth
    return false;
  }
};

/**
 * Sets the Google Sign-In module and status codes for testing purposes.
 * This function allows injecting mock Google Sign-In objects for unit testing.
 * @param googleSignin The mock GoogleSignin object.
 * @param codes The mock statusCodes object.
 */
export const setGoogleSignInForTesting = (googleSignin: any, codes: any) => {
  GoogleSignin = googleSignin;
  statusCodes = codes;
};

// Initialize on module load
initializeGoogleSignIn();

/**
 * Interface for Google authentication response.
 * Defines the structure of the response returned by Google authentication functions.
 */
export interface GoogleAuthResponse {
  success: boolean;
  message: string;
  error?: any;
  user?: any;
}

/**
 * Configures Google Sign-In with client IDs for Supabase integration.
 * This function sets up the Google Sign-In module using configuration from `getGoogleConfig`.
 * It's crucial for native Google authentication with Supabase.
 * @returns {Promise<void>} A promise that resolves when configuration is complete, or rejects on error.
 */
export const configureGoogleAuth = async (): Promise<void> => {
  try {
    if (!GoogleSignin) {
      return;
    }

    // Skip if already configured
    if (isConfigured) {
      return;
    }

    // Get client IDs from secure configuration
    const config = await getGoogleConfig();

    // CRITICAL: For Supabase integration, webClientId MUST be the Web Application client ID
    // This is the same client ID configured in your Supabase dashboard
    const webClientId = config.webClientId;

    // iOS client ID is only used on iOS for native authentication
    const iosClientId = Platform.OS === 'ios' ? config.iosClientId : undefined;

    if (!webClientId) {
      throw new Error('Missing Web Client ID. Check your configuration in lib/config/google.ts');
    }

    // Configure Google Sign-In
    GoogleSignin.configure({
      webClientId, // MUST be Web Application client ID for Supabase
      iosClientId, // Only used on iOS
      offlineAccess: config.offlineAccess,
      hostedDomain: config.hostedDomain,
      forceCodeForRefreshToken: config.forceCodeForRefreshToken,
    });

    isConfigured = true; // Mark as configured
  } catch (error) {
    console.error('❌ Failed to configure Google Sign-In:', error);
    throw error;
  }
};

/**
 * Checks if Google Play Services are available on Android devices.
 * This is a prerequisite for native Google Sign-In on Android.
 * @returns {Promise<boolean>} A promise that resolves to true if services are available, false otherwise.
 */
export const checkGooglePlayServices = async (): Promise<boolean> => {
  try {
    await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
    return true;
  } catch (error) {
    return false;
  }
};


/**
 * Gets the current session from Supabase.
 * @returns Promise<Session | null> - The Supabase session object or null if not authenticated.
 */
export const getSession = async (): Promise<Session | null> => {
  const { data: { session } } = await supabase.auth.getSession();
  return session;
};

/**
 * Signs out the current user from Supabase.
 * @returns Promise<{ error: Error | null }>
 */
export const signOutUser = async (): Promise<{ error: Error | null }> => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

/**
 * Gets the currently authenticated user from Supabase.
 * @returns Promise<User | null> - The Supabase user object or null if not authenticated.
 */
/**
 * Gets the currently authenticated user from Supabase.
 * @returns Promise<User | null> - The Supabase user object or null if not authenticated.
 */
export const getCurrentUser = async (): Promise<User | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

/**
 * Gets the authenticated user directly from Supabase auth.
 * @returns Promise<{ data: { user: User | null }, error: Error | null }>
 */
export const getAuthenticatedUser = async () => {
  return await supabase.auth.getUser();
};

/**
 * Updates the authenticated user's metadata.
 * @param data The data to update.
 * @returns Promise<void>
 */
export const updateAuthUser = async (data: { full_name: string }): Promise<void> => {
  const { error } = await supabase.auth.updateUser({ data });
  if (error) {
    throw error;
  }
};

/**
 * Sends an email OTP for authentication.
 * @param email The email address to send the OTP to.
 * @returns Promise<{ success: boolean; message?: string }> - Success status and an optional message.
 */
export const sendEmailOTP = async (email: string): Promise<{ success: boolean; message?: string }> => {
  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      shouldCreateUser: true,
    },
  });

  if (error) {
    return { success: false, message: error.message };
  }
  return { success: true };
};

/**
 * Verifies an email OTP.
 * @param email The email address.
 * @param token The OTP token.
 * @returns Promise<{ success: boolean; message?: string }> - Success status and an optional message.
 */
export const verifyEmailOTP = async (email: string, token: string): Promise<{ success: boolean; message?: string }> => {
  const { error } = await supabase.auth.verifyOtp({
    email,
    token,
    type: "email",
  });

  if (error) {
    return { success: false, message: error.message };
  }
  return { success: true };
};

/**
 * Signs in with Google using native modal (2025 approach).
 * Uses signInWithIdToken for Supabase integration.
 * Falls back to web auth if native isn't available.
 * @returns {Promise<GoogleAuthResponse>} A promise that resolves with the authentication response.
 */
export const signInWithGoogleNative = async (): Promise<GoogleAuthResponse> => {
  try {
    // Check if native Google Sign-In is available
    if (!GoogleSignin) {
      // Fallback to web-based Google auth
      // Assuming '@/backend/supabase/services/auth/googleAuthService' is the web auth service
      const { signInWithGoogle } = await import('@/backend/supabase/services/auth/googleAuthService');
      return await signInWithGoogle();
    }

    // Configure Google Sign-In if not already done (should already be configured at app startup)
    if (!isConfigured) {
      await configureGoogleAuth();
    }

    // Check Google Play Services on Android
    if (Platform.OS === 'android') {
      const hasPlayServices = await checkGooglePlayServices();
      if (!hasPlayServices) {
        return {
          success: false,
          message: 'Google services are not available on this device. Please try email login.',
        };
      }
    }

    // IMPORTANT: Sign out first to ensure fresh authentication
    // This prevents using cached credentials and forces the Google modal to appear
    try {
      await GoogleSignin.signOut();
    } catch (signOutError) {
      // Ignore sign out errors - user might not be signed in
    }

    // Perform the native Google Sign-In - this will now show the modal
    const userInfo = await GoogleSignin.signIn();

    if (!userInfo?.data?.idToken) {
      console.error('❌ No ID token received from Google Sign-In');
      return {
        success: false,
        message: 'Unable to complete Google sign-in. Please try again.',
      };
    }

    // Sign in to Supabase using the Google ID token
    const { data, error } = await supabase.auth.signInWithIdToken({
      provider: 'google',
      token: userInfo.data.idToken,
      // nonce: undefined, // Skip nonce for simplicity (configure in Supabase dashboard)
    });

    if (error) {
      console.error('❌ Supabase sign-in error:', error);
      return {
        success: false,
        message: 'Unable to sign in with Google. Please try again.',
        error,
      };
    }
    return {
      success: true,
      message: 'Successfully signed in with Google',
      user: data.user,
    };

  } catch (error: any) {
    console.error('❌ Google Sign-In error:', error);
    console.error('Error details:', {
      code: error.code,
      message: error.message,
      name: error.name,
    });

    // Handle specific Google Sign-In error codes
    if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      return {
        success: false,
        message: 'cancelled', // Special message to indicate cancellation
      };
    } else if (error.code === statusCodes.IN_PROGRESS) {
      return {
        success: false,
        message: 'Google sign-in is already in progress. Please wait.',
      };
    } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      return {
        success: false,
        message: 'Google services are not available. Please try email login.',
      };
    } else {
      // Log additional details for DEVELOPER_ERROR
      if (error.message?.includes('DEVELOPER_ERROR')) {
        console.error('🚨 DEVELOPER_ERROR detected. Common causes:');
        console.error('1. Incorrect webClientId in configuration');
        console.error('2. Missing SHA-1 fingerprint in Google Cloud Console');
        console.error('3. Wrong client ID type (should be Web Application)');
        console.error('4. Client ID not matching Supabase configuration');
      }

      return {
        success: false,
        message: 'Unable to sign in with Google. Please try again.',
        error,
      };
    }
  }
};

/**
 * Signs out from Google.
 * This function signs out the user from their Google account.
 * @returns {Promise<void>} A promise that resolves when sign-out is complete.
 */
export const signOutFromGoogle = async (): Promise<void> => {
  try {
    await GoogleSignin.signOut();
  } catch (error) {
    console.error('Error signing out from Google:', error);
  }
};

/**
 * Checks if a user is currently signed in to Google.
 * @returns {Promise<boolean>} A promise that resolves to true if a user is signed in, false otherwise.
 */
export const isGoogleSignedIn = async (): Promise<boolean> => {
  try {
    const isSignedIn = GoogleSignin.getCurrentUser();
    return isSignedIn !== null;
  } catch (error) {
    console.error('Error checking Google sign-in status:', error);
    return false;
  }
};

/**
 * Gets the current Google user information silently.
 * This function attempts to retrieve the currently signed-in Google user's information without prompting.
 * @returns {Promise<any | null>} A promise that resolves with the user information or null if not found.
 */
export const getCurrentGoogleUser = async () => {
  try {
    const userInfo = await GoogleSignin.signInSilently();
    return userInfo;
  } catch (error) {
    return null;
  }
};

/**
 * Signs in with mobile number and password.
 * @param mobile The mobile number.
 * @param password The password.
 * @returns Promise<{ data: { user: User | null }; error: Error | null }> - Supabase auth response.
 */
export const signInWithMobilePassword = async (mobile: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    phone: `+91${mobile}`,
    password,
  });
  return { data, error };
};

/**
 * Signs in with Google using OAuth with redirect.
 * @param redirectTo The redirect URL after authentication.
 * @param queryParams Optional query parameters for the OAuth request.
 * @returns Promise<{ data: any, error: Error | null }>
 */
export const signInWithGoogleOAuth = async (redirectTo: string, queryParams?: Record<string, string>) => {
  return await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo,
      queryParams,
    },
  });
};

/**
 * Sets the authentication session.
 * @param accessToken The access token.
 * @param refreshToken The refresh token.
 * @returns Promise<{ error: Error | null }>
 */
/**
 * Sets the authentication session.
 * @param accessToken The access token.
 * @param refreshToken The refresh token.
 * @returns Promise<{ error: Error | null }>
 */
export const setAuthSession = async (accessToken: string, refreshToken: string) => {
  return await supabase.auth.setSession({
    access_token: accessToken,
    refresh_token: refreshToken,
  });
};

/**
 * Gets the current Supabase session.
 * @returns Promise<{ data: { session: Session | null }, error: Error | null }>
 */
export const getSupabaseSession = async () => {
  return await supabase.auth.getSession();
};

/**
 * Subscribes to Supabase authentication state changes.
 * @param callback A callback function to be called on auth state change.
 * @returns A subscription object with an unsubscribe method.
 */
export const onSupabaseAuthStateChange = (callback: (event: string, session: Session | null) => void) => {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(callback);
  return subscription;
};

/**
 * Signs up a new user with email, password, and full name.
 * @param email The user's email address.
 * @param password The user's chosen password.
 * @param fullName The user's full name.
 * @returns Promise<{ data: { user: User | null }; error: Error | null }> - Supabase auth response.
 */
export const signUpUser = async (email: string, password: string, fullName: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
        name: fullName, // Also store as 'name' for compatibility
      },
    },
  });
  return { data, error };
};

/**
 * Updates the authenticated user's full name.
 * @param fullName The new full name for the user.
 * @returns Promise<{ error: Error | null }>
 */
export const updateUserFullName = async (fullName: string) => {
  const { error } = await supabase.auth.updateUser({
    data: { full_name: fullName },
  });
  return { error };
};
