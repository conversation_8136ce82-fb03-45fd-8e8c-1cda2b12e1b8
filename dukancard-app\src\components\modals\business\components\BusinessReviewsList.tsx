
import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { fetchBusinessReviews } from "@/src/config/supabase/services/reviewService";
import { useAuth } from "@/src/contexts/AuthContext";
import { createReviewsModalStyles } from "@/styles/modals/customer/reviews-modal";
import { ReviewSortOption } from "@/src/components/modals/customer/components/ReviewsSortBottomSheet";

interface BusinessReviewsListProps {
  businessId: string;
  sortBy: ReviewSortOption;
  searchTerm: string;
  reviewType: "received" | "given";
  onReviewCountChange: (count: number) => void;
}

const BusinessReviewsList: React.FC<BusinessReviewsListProps> = ({
  businessId,
  sortBy,
  searchTerm,
  reviewType,
  onReviewCountChange,
}) => {
  const theme = useTheme();
  const styles = createReviewsModalStyles(theme) as ReturnType<typeof createReviewsModalStyles>;
  const { user } = useAuth();
  const [reviews, setReviews] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchReviews = useCallback(async () => {
    if (!hasMore || loading) return;

    setLoading(true);
    try {
      const { items: data, error, totalCount } = await fetchBusinessReviews(
        businessId,
        user?.id,
        sortBy,
        searchTerm,
        reviewType,
        page,
        20
      );

      if (error) throw new Error(typeof error === 'string' ? error : String(error));

      setReviews((prev) => (page === 1 ? data || [] : [...prev, ...(data || [])]));
      setHasMore((data?.length || 0) === 20);
      if (totalCount !== null) {
        onReviewCountChange(totalCount);
      }
    } catch (error) {
      console.error("Error fetching reviews:", error);
    } finally {
      setLoading(false);
    }
  }, [
    businessId,
    sortBy,
    searchTerm,
    reviewType,
    page,
    hasMore,
    loading,
    user?.id,
    onReviewCountChange,
  ]);

  useEffect(() => {
    setReviews([]);
    setPage(1);
    setHasMore(true);
  }, [searchTerm, sortBy, reviewType]);

  useEffect(() => {
    fetchReviews();
  }, [fetchReviews]);

  const renderItem = ({ item }: { item: any }) => (
    <View style={styles.listItemContainer}>
      <Text style={styles.listItemText}>
        {reviewType === "received" ? item.user.name : item.business.name}
      </Text>
      <Text style={styles.listItemRating}>{item.rating}</Text>
      <Text style={styles.listItemContent}>{item.content}</Text>
    </View>
  );

  const renderFooter = () => {
    if (!loading) return null;
    return <ActivityIndicator style={{ marginVertical: 20 }} />;
  };

  const handleLoadMore = () => {
    if (hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  return (
    <FlatList
      data={reviews}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      ListFooterComponent={renderFooter}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      showsVerticalScrollIndicator={false}
    />
  );
};

export default BusinessReviewsList;
